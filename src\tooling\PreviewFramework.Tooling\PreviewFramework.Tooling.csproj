<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis" Version="4.14.0" />
    <PackageReference Include="StreamJsonRpc" Version="2.22.11" />
    <PackageReference Include="Magick.NET-Q8-AnyCPU" Version="13.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\PreviewFramework\PreviewFramework.csproj" />
    <ProjectReference Include="..\..\PreviewFramework.SharedModel\PreviewFramework.SharedModel.csproj" />
  </ItemGroup>

</Project>
